# 企业级 Groovy DSL 工作流系统 - 开发文档

## 1. 系统概述

### 1.1 项目背景
企业级工作流系统旨在提供一个灵活、可扩展的业务流程编排平台，支持复杂的业务逻辑表达和执行。

### 1.2 核心特性
- **声明式 DSL**: 基于 Groovy 的领域特定语言
- **异步执行**: 支持异步、并行、延迟执行
- **多样化节点**: 逻辑判断、通信、数据处理等
- **高可用性**: 集群部署、故障转移
- **监控告警**: 实时监控、性能统计
- **扩展性**: 插件化架构、自定义节点

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   DSL Parser    │───▶│  Workflow Engine │───▶│  Execution Pool │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Validation    │    │   Persistence   │    │   Monitoring    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 核心模块

#### 2.2.1 DSL 解析器 (DSL Parser)
- **词法分析**: 解析 Groovy DSL 语法
- **语法验证**: 检查工作流定义的合法性
- **AST 构建**: 构建抽象语法树
- **优化器**: 流程优化和静态分析

#### 2.2.2 工作流引擎 (Workflow Engine)
- **流程调度**: 管理工作流的执行顺序
- **状态管理**: 维护执行状态和上下文
- **条件判断**: 处理分支和循环逻辑
- **错误处理**: 异常捕获和恢复机制

#### 2.2.3 执行池 (Execution Pool)
- **线程池管理**: 动态线程池，支持不同优先级
- **异步执行**: 非阻塞任务执行
- **并行控制**: 并发度控制和资源隔离
- **延迟调度**: 定时任务和延迟执行

#### 2.2.4 节点类型系统
- **逻辑节点**: 条件判断、循环、分支
- **通信节点**: 邮件、微信、HTTP 调用
- **数据节点**: 数据转换、验证、存储
- **控制节点**: 等待、睡眠、超时

## 3. DSL 语法设计

### 3.1 基础语法

```groovy
workflow "订单处理流程" {
    // 变量定义
    variables {
        orderId = input("orderId")
        userEmail = ""
        orderStatus = "pending"
    }
    
    // 开始节点
    start "订单验证" {
        validate {
            orderId != null && orderId != ""
        }
        onSuccess { context ->
            context.orderStatus = "validated"
        }
        onError { context, error ->
            notify.email("<EMAIL>", "订单验证失败: ${error.message}")
            terminate()
        }
    }
    
    // 条件判断
    decision "检查库存" {
        condition { context ->
            def stock = database.query("SELECT stock FROM products WHERE order_id = ?", context.orderId)
            return stock > 0
        }
        
        whenTrue {
            goto "库存充足处理"
        }
        
        whenFalse {
            goto "库存不足处理"
        }
    }
    
    // 并行执行
    parallel "订单处理" {
        branch "库存扣减" {
            execute { context ->
                database.update("UPDATE products SET stock = stock - 1 WHERE order_id = ?", context.orderId)
            }
        }
        
        branch "发送通知" {
            email {
                to { context -> getUserEmail(context.orderId) }
                subject "订单确认"
                template "order_confirmation.html"
                data { context -> [orderId: context.orderId] }
            }
        }
        
        // 等待所有分支完成
        waitAll()
        
        // 或者等待任意一个分支完成
        // waitAny()
    }
    
    // 异步任务
    async "日志记录" {
        delay 5.seconds
        execute { context ->
            logger.info("订单 ${context.orderId} 处理完成")
        }
    }
    
    // 循环处理
    loop "重试机制" {
        maxRetries 3
        interval 30.seconds
        
        condition { context ->
            // 检查外部系统状态
            !checkExternalSystemReady()
        }
        
        execute { context ->
            callExternalAPI(context.orderId)
        }
        
        onMaxRetriesReached {
            escalate "外部系统调用失败"
        }
    }
    
    // 子工作流调用
    subflow "支付处理" {
        workflow "payment_workflow"
        input { context ->
            [orderId: context.orderId, amount: context.orderAmount]
        }
        output { context, result ->
            context.paymentId = result.paymentId
            context.paymentStatus = result.status
        }
    }
    
    // 结束节点
    end "订单完成" {
        execute { context ->
            context.orderStatus = "completed"
            database.update("UPDATE orders SET status = ? WHERE id = ?", 
                           context.orderStatus, context.orderId)
        }
    }
}
```

### 3.2 高级特性

#### 3.2.1 错误处理
```groovy
workflow "容错处理示例" {
    
    // 全局错误处理
    errorHandler {
        onTimeout { context, timeoutException ->
            notify.wechat("工作流超时: ${context.workflowId}")
            escalate()
        }
        
        onException { context, exception ->
            if (exception instanceof CriticalException) {
                terminate()
            } else {
                retry 3.times
            }
        }
    }
    
    // 节点级错误处理
    task "关键业务操作" {
        timeout 30.seconds
        
        execute { context ->
            // 业务逻辑
        }
        
        onError { context, error ->
            // 自定义错误处理
            rollback { context ->
                // 回滚操作
            }
        }
    }
}
```

#### 3.2.2 数据流处理
```groovy
workflow "数据处理管道" {
    
    dataflow {
        source "用户数据" {
            from database.table("users")
            batchSize 1000
        }
        
        transform "数据清洗" {
            filter { row -> row.age >= 18 }
            map { row -> 
                [
                    id: row.id,
                    name: row.name.trim(),
                    email: row.email.toLowerCase()
                ]
            }
        }
        
        sink "输出结果" {
            to database.table("processed_users")
            onBatchComplete { batchCount ->
                logger.info("已处理 ${batchCount} 批数据")
            }
        }
    }
}
```

## 4. 节点类型详细设计

### 4.1 逻辑节点

#### 4.1.1 条件判断节点 (Decision)
```groovy
decision "条件检查" {
    condition { context -> /* 返回 boolean */ }
    whenTrue { /* 真分支 */ }
    whenFalse { /* 假分支 */ }
    timeout 10.seconds  // 可选超时
}
```

#### 4.1.2 多路分支节点 (Switch)
```groovy
switch "状态分发" {
    expression { context -> context.orderStatus }
    
    case "pending" {
        goto "待处理流程"
    }
    
    case "processing" {
        goto "处理中流程"
    }
    
    case "completed" {
        goto "完成流程"
    }
    
    default {
        error "未知状态: ${context.orderStatus}"
    }
}
```

#### 4.1.3 循环节点 (Loop)
```groovy
// While 循环
whileLoop "等待条件满足" {
    condition { context -> !isCompleted(context.taskId) }
    maxIterations 100
    interval 5.seconds
    
    execute { context ->
        checkTaskStatus(context.taskId)
    }
}

// For 循环
forLoop "批量处理" {
    items { context -> context.userIds }
    parallel true  // 是否并行处理
    maxConcurrency 10
    
    execute { context, item ->
        processUser(item)
    }
}
```

### 4.2 通信节点

#### 4.2.1 邮件节点 (Email)
```groovy
email "发送通知邮件" {
    to { context -> [context.userEmail, "<EMAIL>"] }
    cc { context -> context.ccList }
    bcc "<EMAIL>"
    
    subject { context -> "订单 ${context.orderId} 状态更新" }
    
    // 文本内容
    text { context ->
        """
        尊敬的用户，
        您的订单 ${context.orderId} 状态已更新为：${context.orderStatus}
        """
    }
    
    // HTML 模板
    html {
        template "email/order_status.html"
        data { context ->
            [
                orderId: context.orderId,
                status: context.orderStatus,
                updateTime: new Date()
            ]
        }
    }
    
    // 附件
    attachments { context ->
        [
            [name: "receipt.pdf", content: generateReceipt(context.orderId)],
            [name: "invoice.xlsx", path: "/tmp/invoice_${context.orderId}.xlsx"]
        ]
    }
    
    // 邮件配置
    config {
        smtp {
            host "smtp.company.com"
            port 587
            username "<EMAIL>"
            password env("SMTP_PASSWORD")
            tls true
        }
    }
    
    // 发送选项
    priority "high"  // low, normal, high
    deliveryTime "2024-01-15 09:00:00"  // 延迟发送
    
    onSuccess { context ->
        logger.info("邮件发送成功")
    }
    
    onError { context, error ->
        logger.error("邮件发送失败: ${error.message}")
        retry 3.times delay 1.minute
    }
}
```

#### 4.2.2 企业微信节点 (WeChat)
```groovy
wechat "企业微信通知" {
    webhook "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx"
    
    // 文本消息
    text { context ->
        """
        📋 工作流通知
        流程：${context.workflowName}
        状态：${context.status}
        时间：${new Date()}
        """
    }
    
    // Markdown 消息
    markdown { context ->
        """
        ## 订单处理完成
        - **订单号**: ${context.orderId}
        - **状态**: <font color="info">${context.status}</font>
        - **处理时间**: ${context.processTime}
        
        > 请相关人员及时跟进
        """
    }
    
    // 图片消息
    image {
        base64 { context -> generateChartImage(context.data) }
        // 或者 url
        // url "https://example.com/chart.png"
    }
    
    // 文件消息
    file {
        mediaId { context -> uploadFile(context.reportPath) }
    }
    
    // @指定用户
    mention {
        users ["zhangsan", "lisi"]  // 用户ID
        phones ["13800138000"]      // 手机号
        all false                   // 是否@所有人
    }
    
    onSuccess { context ->
        context.notificationSent = true
    }
}
```

#### 4.2.3 HTTP 调用节点
```groovy
http "调用外部API" {
    url { context -> "https://api.example.com/orders/${context.orderId}" }
    method "POST"
    
    headers {
        "Content-Type" "application/json"
        "Authorization" { context -> "Bearer ${context.accessToken}" }
        "X-Request-ID" { context -> UUID.randomUUID().toString() }
    }
    
    body { context ->
        [
            orderId: context.orderId,
            status: context.status,
            timestamp: System.currentTimeMillis()
        ]
    }
    
    // 请求配置
    timeout 30.seconds
    retries 3
    retryDelay 5.seconds
    
    // 响应处理
    onResponse { context, response ->
        if (response.status == 200) {
            def result = response.json()
            context.externalOrderId = result.externalId
        } else {
            throw new RuntimeException("API调用失败: ${response.status}")
        }
    }
    
    // SSL配置
    ssl {
        trustAll false
        keyStore "/path/to/keystore.jks"
        keyStorePassword env("KEYSTORE_PASSWORD")
    }
}
```

#### 4.2.4 短信节点 (SMS)
```groovy
sms "发送短信通知" {
    to { context -> context.userPhone }
    
    message { context ->
        "【公司名】您的订单${context.orderId}已发货，预计3-5个工作日送达。"
    }
    
    // 短信模板
    template {
        id "SMS_001"
        params { context ->
            [
                orderNo: context.orderId,
                deliveryDays: "3-5"
            ]
        }
    }
    
    provider "aliyun"  // 服务商: aliyun, tencent, huawei
    
    config {
        accessKey env("SMS_ACCESS_KEY")
        secretKey env("SMS_SECRET_KEY")
        signName "公司名"
    }
}
```

### 4.3 数据处理节点

#### 4.3.1 数据库操作节点
```groovy
database "数据库操作" {
    connection "primary"  // 数据源名称
    
    // 查询操作
    query { context ->
        sql "SELECT * FROM orders WHERE user_id = ? AND status = ?"
        params [context.userId, "pending"]
        
        onResult { context, rows ->
            context.pendingOrders = rows
        }
    }
    
    // 更新操作
    update { context ->
        sql "UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?"
        params [context.newStatus, context.orderId]
        
        onResult { context, affectedRows ->
            logger.info("更新了 ${affectedRows} 条记录")
        }
    }
    
    // 批量操作
    batch { context ->
        sql "INSERT INTO order_logs (order_id, action, created_at) VALUES (?, ?, ?)"
        batchData { context ->
            context.actions.collect { action ->
                [context.orderId, action, new Date()]
            }
        }
    }
    
    // 事务处理
    transaction { context ->
        operations {
            update "UPDATE inventory SET quantity = quantity - ? WHERE product_id = ?"
            params [context.quantity, context.productId]
            
            insert "INSERT INTO order_items (order_id, product_id, quantity) VALUES (?, ?, ?)"
            params [context.orderId, context.productId, context.quantity]
        }
        
        onCommit { context ->
            logger.info("事务提交成功")
        }
        
        onRollback { context, error ->
            logger.error("事务回滚: ${error.message}")
        }
    }
}
```

#### 4.3.2 数据转换节点
```groovy
transform "数据转换" {
    input { context -> context.rawData }
    
    // 字段映射
    mapping {
        "user_name" -> "userName"
        "user_email" -> "email"
        "created_time" -> { value -> Date.parse("yyyy-MM-dd HH:mm:ss", value) }
    }
    
    // 数据验证
    validation {
        required ["userName", "email"]
        
        rules {
            userName { value -> value.length() >= 2 }
            email { value -> value.matches(/^[\w\.-]+@[\w\.-]+\.\w+$/) }
        }
    }
    
    // 数据清洗
    cleaning {
        trim ["userName", "email"]
        toLowerCase ["email"]
        removeEmpty true
    }
    
    // 数据聚合
    aggregation {
        groupBy "department"
        sum "salary"
        count "employees"
    }
    
    output { context, transformedData ->
        context.processedData = transformedData
    }
}
```

#### 4.3.3 文件处理节点
```groovy
file "文件处理" {
    // 文件读取
    read { context ->
        path { context -> "/data/input/${context.fileName}" }
        format "csv"  // csv, json, xml, excel
        encoding "UTF-8"
        
        options {
            delimiter ","
            hasHeader true
            skipEmptyLines true
        }
        
        onData { context, data ->
            context.fileContent = data
        }
    }
    
    // 文件写入
    write { context ->
        path { context -> "/data/output/result_${context.timestamp}.json" }
        content { context -> context.result }
        format "json"
        
        options {
            pretty true
            compression "gzip"
        }
    }
    
    // 文件上传
    upload { context ->
        localPath { context -> context.localFile }
        remotePath { context -> "s3://bucket/files/${context.fileName}" }
        
        provider "s3"  // s3, ftp, sftp, oss
        
        config {
            region "us-east-1"
            accessKey env("AWS_ACCESS_KEY")
            secretKey env("AWS_SECRET_KEY")
        }
        
        onProgress { context, progress ->
            logger.info("上传进度: ${progress.percentage}%")
        }
    }
}
```

### 4.4 控制节点

#### 4.4.1 等待节点 (Wait)
```groovy
// 固定时间等待
wait "等待5分钟" {
    duration 5.minutes
}

// 条件等待
waitFor "等待条件满足" {
    condition { context -> checkExternalStatus(context.taskId) }
    timeout 10.minutes
    checkInterval 30.seconds
    
    onTimeout { context ->
        logger.warn("等待超时，继续执行")
    }
}

// 信号等待
waitSignal "等待外部信号" {
    signal { context -> "order_${context.orderId}_completed" }
    timeout 1.hour
    
    onSignalReceived { context, signalData ->
        context.signalData = signalData
    }
}
```

#### 4.4.2 延迟节点 (Delay)
```groovy
delay "延迟执行" {
    // 固定延迟
    time 2.hours
    
    // 动态延迟
    time { context -> 
        calculateDelayTime(context.priority) 
    }
    
    // 定时执行
    schedule {
        cron "0 9 * * MON-FRI"  // 工作日上午9点
        timezone "Asia/Shanghai"
    }
    
    // 延迟策略
    strategy "exponential_backoff"  // fixed, exponential_backoff, linear_backoff
    
    onDelayStart { context ->
        logger.info("开始延迟执行")
    }
}
```

#### 4.4.3 屏障节点 (Barrier)
```groovy
barrier "同步屏障" {
    // 等待多个分支完成
    waitFor ["branch1", "branch2", "branch3"]
    
    // 超时处理
    timeout 30.minutes
    
    onAllComplete { context ->
        logger.info("所有分支执行完成")
    }
    
    onTimeout { context ->
        logger.warn("等待超时，强制继续")
    }
}
```

## 5. 执行引擎设计

### 5.1 线程池管理

```groovy
threadPools {
    // 主执行池
    main {
        coreSize 10
        maxSize 50
        queueCapacity 1000
        keepAlive 60.seconds
        namePrefix "workflow-main-"
        rejectedExecutionHandler "CallerRunsPolicy"
    }
    
    // IO密集型任务池
    io {
        coreSize 20
        maxSize 100
        queueCapacity 5000
        keepAlive 120.seconds
        namePrefix "workflow-io-"
    }
    
    // CPU密集型任务池
    compute {
        coreSize Runtime.runtime.availableProcessors()
        maxSize Runtime.runtime.availableProcessors() * 2
        queueCapacity 100
        namePrefix "workflow-compute-"
    }
    
    // 定时任务池
    scheduled {
        coreSize 5
        namePrefix "workflow-scheduled-"
    }
    
    // 自定义任务池
    custom "priority_pool" {
        type "PriorityThreadPoolExecutor"
        coreSize 10
        maxSize 20
        comparator { task1, task2 ->
            task1.priority.compareTo(task2.priority)
        }
    }
}
```

### 5.2 异步执行

```groovy
workflow "异步执行示例" {
    
    // 异步任务
    async "异步处理" {
        threadPool "io"
        timeout 5.minutes
        
        execute { context ->
            // 长时间运行的任务
            processLargeFile(context.filePath)
        }
        
        onComplete { context, result ->
            context.asyncResult = result
        }
        
        onTimeout { context ->
            logger.warn("异步任务超时")
        }
    }
    
    // 异步并行
    asyncParallel "并行异步任务" {
        maxConcurrency 5
        
        tasks { context ->
            context.taskIds.collect { taskId ->
                {
                    processTask(taskId)
                }
            }
        }
        
        onEachComplete { context, taskResult ->
            context.completedTasks << taskResult
        }
        
        onAllComplete { context ->
            logger.info("所有异步任务完成")
        }
    }
    
    // 异步回调
    asyncCallback "外部系统调用" {
        execute { context ->
            callExternalSystem(context.data) { result ->
                // 异步回调
                context.externalResult = result
                continueWorkflow()
            }
        }
        
        timeout 10.minutes
        
        onTimeout { context ->
            cancelExternalCall()
        }
    }
}
```

### 5.3 并行执行

```groovy
workflow "并行执行模式" {
    
    // Fork-Join 模式
    forkJoin "数据处理" {
        fork {
            branch "数据验证" {
                execute { context -> validateData(context.data) }
            }
            
            branch "数据转换" {
                execute { context -> transformData(context.data) }
            }
            
            branch "数据存储" {
                execute { context -> storeData(context.data) }
            }
        }
        
        join { context, results ->
            context.processResults = results
        }
        
        // 异常处理
        onBranchError { context, branchName, error ->
            logger.error("分支 ${branchName} 执行失败: ${error.message}")
            // 可以选择取消其他分支或继续执行
            cancelOtherBranches()
        }
    }
    
    // 管道并行
    pipeline "数据管道" {
        stages {
            stage "读取" {
                parallelism 2
                execute { context -> readData() }
            }
            
            stage "处理" {
                parallelism 4
                execute { context, data -> processData(data) }
            }
            
            stage "输出" {
                parallelism 1
                execute { context, data -> outputData(data) }
            }
        }
        
        bufferSize 100
        backpressure "block"  // block, drop, buffer
    }
    
    // 生产者-消费者模式
    producerConsumer "消息处理" {
        producer {
            threadPool "io"
            batchSize 10
            
            produce { context ->
                // 生产消息
                generateMessages()
            }
        }
        
        consumer {
            threadPool "compute"
            parallelism 3
            
            consume { context, message ->
                // 消费消息
                processMessage(message)
            }
        }
        
        queue {
            capacity 1000
            type "LinkedBlockingQueue"
        }
    }
}
```

## 6. 监控和日志

### 6.1 指标收集

```groovy
monitoring {
    // 执行指标
    metrics {
        execution {
            duration true          // 执行时长
            throughput true        // 吞吐量
            successRate true       // 成功率
            errorRate true         // 错误率
            queueSize true         // 队列大小
            activeThreads true     // 活跃线程数
        }
        
        business {
            custom "orderProcessTime" {
                type "histogram"
                tags ["department", "orderType"]
            }
            
            custom "userActivity" {
                type "counter"
                tags ["action", "source"]
            }
        }
    }
    
    // 告警规则
    alerts {
        alert "执行时间过长" {
            condition "workflow.execution.duration > 300"
            severity "warning"
            frequency "5m"
            
            actions {
                email ["<EMAIL>"]
                wechat "ops_group"
            }
        }
        
        alert "错误率过高" {
            condition "workflow.error.rate > 0.1"
            severity "critical"
            frequency "1m"
            
            actions {
                escalate "incident_manager"
                autoScale {
                    threadPool "main"
                    scale 2.0
                }
            }
        }
    }
    
    // 性能分析
    profiling {
        enabled true
        sampleRate 0.1
        
        exporters {
            prometheus {
                endpoint "/metrics"
                port 9090
            }
            
            grafana {
                url "http://grafana:3000"
                dashboard "workflow_dashboard"
            }
            
            jaeger {
                endpoint "http://jaeger:14268"
                serviceName "workflow-engine"
            }
        }
    }
}
```

### 6.2 日志系统

```groovy
logging {
    // 日志级别
    level "INFO"
    
    // 日志格式
    pattern "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    
    // 日志输出
    appenders {
        console {
            enabled true
            threshold "INFO"
        }
        
        file {
            name "workflow.log"
            path "/var/log/workflow/"
            maxSize "100MB"
            maxFiles 10
            compress true
        }
        
        elasticsearch {
            cluster "log-cluster"
            index "workflow-logs"
            type "workflow"
        }
    }
    
    // 结构化日志
    structured {
        enabled true
        fields {
            workflowId true
            nodeId true
            executionId true
            userId true
            timestamp true
            duration true
            status true
            error true
        }
    }
    
    // 审计日志
    audit {
        enabled true
        events ["start", "complete", "error", "timeout"]
        
        fields {
            actor true
            action true
            resource true
            timestamp true
            outcome true
            details true
        }
        
        storage {
            type "database"
            table "workflow_audit"
            retention "2y"
        }
    }
}
```

## 7. 持久化设计

### 7.1 数据模型

```sql
-- 工作流定义表
CREATE TABLE workflow_definitions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    version VARCHAR(50) NOT NULL,
    dsl_content TEXT NOT NULL,
    status ENUM('active', 'inactive', 'deprecated') DEFAULT 'active',
    created_by VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_name_version (name, version)
);

-- 工作流实例表
CREATE TABLE workflow_instances (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workflow_id BIGINT NOT NULL,
    execution_id VARCHAR(100) UNIQUE NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    input_data JSON,
    output_data JSON,
    context_data JSON,
    started_by VARCHAR(100),
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    error_message TEXT,
    retry_count INT DEFAULT 0,
    parent_execution_id VARCHAR(100),
    FOREIGN KEY (workflow_id) REFERENCES workflow_definitions(id),
    INDEX idx_status (status),
    INDEX idx_started_at (started_at)
);

-- 节点执行记录表
CREATE TABLE node_executions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    execution_id VARCHAR(100) NOT NULL,
    node_id VARCHAR(100) NOT NULL,
    node_type VARCHAR(50) NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed', 'skipped') DEFAULT 'pending',
    input_data JSON,
    output_data JSON,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    duration_ms BIGINT,
    error_message TEXT,
    retry_count INT DEFAULT 0,
    thread_name VARCHAR(100),
    FOREIGN KEY (execution_id) REFERENCES workflow_instances(execution_id),
    INDEX idx_execution_id (execution_id),
    INDEX idx_status (status)
);

-- 调度任务表
CREATE TABLE scheduled_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    execution_id VARCHAR(100) NOT NULL,
    node_id VARCHAR(100) NOT NULL,
    scheduled_time TIMESTAMP NOT NULL,
    actual_time TIMESTAMP,
    status ENUM('pending', 'executed', 'cancelled') DEFAULT 'pending',
    task_data JSON,
    INDEX idx_scheduled_time (scheduled_time),
    INDEX idx_status (status)
);
```

### 7.2 状态管理

```groovy
stateManagement {
    // 状态存储
    storage {
        primary {
            type "mysql"
            url "************************************"
            username "workflow_user"
            password env("DB_PASSWORD")
            
            pool {
                minSize 5
                maxSize 20
                maxWait 30.seconds
            }
        }
        
        cache {
            type "redis"
            cluster ["redis1:6379", "redis2:6379", "redis3:6379"]
            
            config {
                timeout 5.seconds
                maxRetries 3
                keyPrefix "workflow:"
                ttl 1.hour
            }
        }
        
        backup {
            type "s3"
            bucket "workflow-backup"
            region "us-east-1"
            
            schedule {
                full "0 2 * * SUN"    // 每周日凌晨2点全量备份
                incremental "0 2 * * *"  // 每天凌晨2点增量备份
            }
        }
    }
    
    // 状态同步
    synchronization {
        strategy "eventual_consistency"  // strong_consistency, eventual_consistency
        
        conflictResolution {
            strategy "last_write_wins"  // manual, last_write_wins, version_vector
        }
        
        replication {
            factor 3
            consistency "quorum"
        }
    }
    
    // 状态恢复
    recovery {
        checkpoint {
            interval 5.minutes
            storage "persistent"
        }
        
        rollback {
            enabled true
            maxLevels 10
        }
        
        corruption {
            detection true
            autoRepair false
            notification ["<EMAIL>"]
        }
    }
}
```

## 8. 安全设计

### 8.1 认证授权

```groovy
security {
    // 认证配置
    authentication {
        providers {
            ldap {
                url "ldap://ldap.company.com:389"
                baseDn "dc=company,dc=com"
                userSearchFilter "(uid={0})"
                groupSearchFilter "(member={0})"
            }
            
            oauth2 {
                provider "company_sso"
                clientId env("OAUTH2_CLIENT_ID")
                clientSecret env("OAUTH2_CLIENT_SECRET")
                authorizationUri "https://sso.company.com/oauth/authorize"
                tokenUri "https://sso.company.com/oauth/token"
            }
            
            jwt {
                secret env("JWT_SECRET")
                expiration 8.hours
                issuer "workflow-engine"
            }
        }
        
        multiAuth {
            enabled true
            order ["oauth2", "ldap", "jwt"]
        }
    }
    
    // 授权配置
    authorization {
        model "rbac"  // rbac, abac
        
        roles {
            admin {
                permissions ["*"]
                description "系统管理员"
            }
            
            developer {
                permissions [
                    "workflow:create",
                    "workflow:read",
                    "workflow:update",
                    "workflow:test"
                ]
                description "开发人员"
            }
            
            operator {
                permissions [
                    "workflow:read",
                    "workflow:execute",
                    "workflow:monitor"
                ]
                description "运维人员"
            }
            
            viewer {
                permissions ["workflow:read"]
                description "只读用户"
            }
        }
        
        resources {
            workflow {
                pattern "/workflows/{workflowId}"
                owner true  // 支持资源拥有者
            }
            
            execution {
                pattern "/executions/{executionId}"
                inherit "workflow"
            }
        }
        
        policies {
            // 基于时间的访问控制
            timeBasedAccess {
                enabled true
                rules {
                    production {
                        schedule "09:00-18:00"
                        timezone "Asia/Shanghai"
                        days ["MON", "TUE", "WED", "THU", "FRI"]
                    }
                }
            }
            
            // 基于IP的访问控制
            ipBasedAccess {
                enabled true
                whitelist ["***********/24", "10.0.0.0/8"]
                blacklist ["0.0.0.0/0"]
            }
        }
    }
    
    // 审计日志
    audit {
        enabled true
        
        events {
            authentication ["login", "logout", "failed_login"]
            authorization ["access_granted", "access_denied"]
            workflow ["create", "update", "delete", "execute"]
            system ["startup", "shutdown", "configuration_change"]
        }
        
        storage {
            type "elasticsearch"
            index "security-audit"
            retention "7y"
        }
        
        realtime {
            suspicious_activity {
                threshold 5
                window 5.minutes
                action "block_user"
            }
        }
    }
}
```

### 8.2 数据安全

```groovy
dataSecurity {
    // 数据加密
    encryption {
        atRest {
            algorithm "AES-256-GCM"
            keyManagement "vault"
            
            fields ["password", "apiKey", "sensitiveData"]
        }
        
        inTransit {
            tls {
                version "1.3"
                cipherSuites ["TLS_AES_256_GCM_SHA384"]
                certificateValidation true
            }
            
            messageLevel {
                algorithm "RSA-OAEP"
                keyLength 2048
            }
        }
        
        keyRotation {
            interval 90.days
            autoRotate true
            gracePeriod 7.days
        }
    }
    
    // 数据脱敏
    masking {
        rules {
            email {
                pattern /^([\w\.-]+)@([\w\.-]+)$/
                replacement { match -> "${match[1].take(2)}***@${match[2]}" }
            }
            
            phone {
                pattern /^(\d{3})\d{4}(\d{4})$/
                replacement { match -> "${match[1]}****${match[2]}" }
            }
            
            idCard {
                pattern /^(\d{6})\d{8}(\d{4})$/
                replacement { match -> "${match[1]}********${match[2]}" }
            }
        }
        
        contexts {
            logging true
            monitoring true
            export true
            testing true
        }
    }
    
    // 数据分类
    classification {
        levels {
            public {
                retention "unlimited"
                encryption false
                masking false
            }
            
            internal {
                retention "5y"
                encryption true
                masking true
            }
            
            confidential {
                retention "3y"
                encryption true
                masking true
                accessControl "strict"
            }
            
            restricted {
                retention "1y"
                encryption true
                masking true
                accessControl "minimal"
                approval true
            }
        }
        
        autoClassification {
            enabled true
            rules {
                pattern /password|secret|key/ -> "confidential"
                pattern /email|phone|address/ -> "internal"
                pattern /public|common/ -> "public"
            }
        }
    }
}
```

## 9. 部署和运维

### 9.1 容器化部署

```dockerfile
# Dockerfile
FROM openjdk:11-jre-slim

# 安装依赖
RUN apt-get update && apt-get install -y \
    curl \
    jq \
    && rm -rf /var/lib/apt/lists/*

# 创建应用目录
WORKDIR /app

# 复制应用文件
COPY target/workflow-engine.jar app.jar
COPY config/ config/
COPY scripts/ scripts/

# 创建非root用户
RUN useradd -r -s /bin/false workflow
USER workflow

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080 9090

# 启动命令
ENTRYPOINT ["java", "-jar", "app.jar"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  workflow-engine:
    build: .
    ports:
      - "8080:8080"
      - "9090:9090"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql
      - REDIS_CLUSTER=redis1:6379,redis2:6379,redis3:6379
    volumes:
      - ./logs:/app/logs
      - ./workflows:/app/workflows
    depends_on:
      - mysql
      - redis1
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: workflow
      MYSQL_USER: workflow_user
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    command: --default-authentication-plugin=mysql_native_password

  redis1:
    image: redis:7-alpine
    command: redis-server --cluster-enabled yes --cluster-config-file nodes.conf
    volumes:
      - redis1_data:/data
    ports:
      - "6379:6379"

  redis2:
    image: redis:7-alpine
    command: redis-server --cluster-enabled yes --cluster-config-file nodes.conf
    volumes:
      - redis2_data:/data
    ports:
      - "6380:6379"

  redis3:
    image: redis:7-alpine
    command: redis-server --cluster-enabled yes --cluster-config-file nodes.conf
    volumes:
      - redis3_data:/data
    ports:
      - "6381:6379"

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - workflow-engine

volumes:
  mysql_data:
  redis1_data:
  redis2_data:
  redis3_data:
```

### 9.2 Kubernetes 部署

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: workflow-engine
  namespace: workflow
  labels:
    app: workflow-engine
    version: v1.0.0
spec:
  replicas: 3
  selector:
    matchLabels:
      app: workflow-engine
  template:
    metadata:
      labels:
        app: workflow-engine
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: workflow-engine
      
      # 初始化容器
      initContainers:
      - name: wait-for-mysql
        image: busybox:1.35
        command: ['sh', '-c', 'until nc -z mysql 3306; do sleep 1; done']
      
      - name: migrate-db
        image: migrate/migrate
        command:
        - migrate
        - -path=/migrations
        - -database=mysql://user:pass@mysql:3306/workflow
        - up
        volumeMounts:
        - name: migrations
          mountPath: /migrations
      
      containers:
      - name: workflow-engine
        image: workflow-engine:v1.0.0
        imagePullPolicy: IfNotPresent
        
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        - containerPort: 9090
          name: metrics
          protocol: TCP
        
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: DB_HOST
          value: "mysql"
        - name: REDIS_CLUSTER
          value: "redis-headless:6379"
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        
        envFrom:
        - secretRef:
            name: workflow-secrets
        - configMapRef:
            name: workflow-config
        
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2
            memory: 2Gi
        
        livenessProbe:
          httpGet:
            path: /actuator/health/liveness
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        startupProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        
        volumeMounts:
        - name: workflow-config
          mountPath: /app/config
        - name: workflow-logs
          mountPath: /app/logs
        - name: workflow-data
          mountPath: /app/data
        
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      
      volumes:
      - name: workflow-config
        configMap:
          name: workflow-config
      - name: workflow-logs
        emptyDir: {}
      - name: workflow-data
        persistentVolumeClaim:
          claimName: workflow-data
      - name: migrations
        configMap:
          name: db-migrations
      
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - workflow-engine
              topologyKey: kubernetes.io/hostname
      
      tolerations:
      - key: "workflow-node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"

---
apiVersion: v1
kind: Service
metadata:
  name: workflow-engine
  namespace: workflow
  labels:
    app: workflow-engine
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: http
  - port: 9090
    targetPort: 9090
    protocol: TCP
    name: metrics
  selector:
    app: workflow-engine

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: workflow-engine
  namespace: workflow
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: basic-auth
spec:
  tls:
  - hosts:
    - workflow.company.com
    secretName: workflow-tls
  rules:
  - host: workflow.company.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: workflow-engine
            port:
              number: 80

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: workflow-engine
  namespace: workflow
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: workflow-engine
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: workflow_queue_size
      target:
        type: AverageValue
        averageValue: "100"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
```

### 9.3 监控配置

```yaml
# prometheus-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    rule_files:
    - "/etc/prometheus/rules/*.yml"
    
    alerting:
      alertmanagers:
      - static_configs:
        - targets:
          - alertmanager:9093
    
    scrape_configs:
    - job_name: 'workflow-engine'
      static_configs:
      - targets: ['workflow-engine:9090']
      metrics_path: /actuator/prometheus
      scrape_interval: 10s
      
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod_name
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
        
    - job_name: 'kubernetes-pods'
      kubernetes_sd_configs:
      - role: pod
      
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__

  workflow-rules.yml: |
    groups:
    - name: workflow.rules
      rules:
      - alert: WorkflowHighErrorRate
        expr: rate(workflow_executions_failed_total[5m]) / rate(workflow_executions_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "工作流错误率过高"
          description: "工作流 {{ $labels.workflow_name }} 在过去5分钟内错误率超过10%"
      
      - alert: WorkflowExecutionTimeout
        expr: histogram_quantile(0.95, rate(workflow_execution_duration_seconds_bucket[5m])) > 300
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "工作流执行时间过长"
          description: "工作流执行时间95%分位数超过5分钟"
      
      - alert: WorkflowQueueBacklog
        expr: workflow_queue_size > 1000
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "工作流队列积压"
          description: "工作流队列大小超过1000，当前值: {{ $value }}"
```

## 10. 性能优化

### 10.1 执行优化

```groovy
performance {
    // 执行优化
    execution {
        // 预编译优化
        precompilation {
            enabled true
            cacheSize 1000
            warmup {
                enabled true
                workflowPatterns ["common_*", "critical_*"]
            }
        }
        
        // 智能调度
        scheduler {
            strategy "adaptive"  // round_robin, least_connections, adaptive
            
            loadBalancing {
                algorithm "weighted_round_robin"
                weights { node ->
                    def cpu = node.cpuUsage
                    def memory = node.memoryUsage
                    def queue = node.queueSize
                    
                    return (100 - cpu) * (100 - memory) / (queue + 1)
                }
            }
            
            workStealing {
                enabled true
                threshold 0.8
                stealRatio 0.5
            }
        }
        
        // 资源隔离
        isolation {
            threadPools {
                strategy "per_workflow_type"
                
                mapping {
                    "batch_*" -> "batch_pool"
                    "realtime_*" -> "realtime_pool"
                    "long_running_*" -> "long_running_pool"
                }
            }
            
            memory {
                strategy "cgroup"
                limits {
                    "batch_*" -> "2GB"
                    "realtime_*" -> "512MB"
                }
            }
            
            cpu {
                strategy "quota"
                limits {
                    "batch_*" -> "50%"
                    "realtime_*" -> "80%"
                }
            }
        }
    }
    
    // 缓存优化
    caching {
        levels {
            l1 {
                type "caffeine"
                maxSize 10000
                expireAfterWrite 5.minutes
                expireAfterAccess 2.minutes
            }
            
            l2 {
                type "redis"
                cluster true
                ttl 1.hour
                compression "gzip"
            }
            
            l3 {
                type "database"
                persistent true
                ttl 1.day
            }
        }
        
        strategies {
            workflowDefinition {
                levels ["l1", "l2"]
                invalidation "version_based"
            }
            
            executionContext {
                levels ["l1"]
                invalidation "time_based"
            }
            
            nodeResults {
                levels ["l1", "l2", "l3"]
                invalidation "manual"
            }
        }
        
        warmup {
            enabled true
            
            strategies {
                popular {
                    metric "execution_count"
                    threshold 100
                    window "7d"
                }
                
                recent {
                    metric "last_access_time"
                    threshold "1h"
                }
            }
        }
    }
    
    // 数据库优化
    database {
        connectionPool {
            type "HikariCP"
            
            config {
                minimumIdle 10
                maximumPoolSize 50
                connectionTimeout 30.seconds
                idleTimeout 10.minutes
                maxLifetime 30.minutes
                leakDetectionThreshold 60.seconds
            }
            
            monitoring {
                enabled true
                slowQueryThreshold 1.second
                connectionLeakDetection true
            }
        }
        
        queryOptimization {
            indexHints true
            queryPlan {
                analysis true
                suggestions true
            }
            
            batchOperations {
                enabled true
                batchSize 1000
                timeout 30.seconds
            }
        }
        
        readWriteSplitting {
            enabled true
            
            routing {
                read ["slave1", "slave2"]
                write ["master"]
                
                rules {
                    "SELECT" -> "read"
                    "INSERT|UPDATE|DELETE" -> "write"
                }
            }
            
            loadBalancing {
                read "round_robin"
                write "master_only"
            }
        }
    }
}
```

### 10.2 内存优化

```groovy
memoryOptimization {
    // GC调优
    garbageCollection {
        collector "G1GC"  // G1GC, ZGC, Parallel
        
        g1Config {
            heapSize "4g"
            newRatio 2
            maxGCPauseMillis 200
            g1HeapRegionSize "16m"
            
            tuning {
                concGCThreads 4
                parallelGCThreads 8
                g1MixedGCCountTarget 8
            }
        }
        
        monitoring {
            gcLogging true
            gcAnalysis true
            
            alerts {
                longPause {
                    threshold 500.milliseconds
                    action "alert"
                }
                
                highFrequency {
                    threshold 10  // per minute
                    action "scale_up"
                }
            }
        }
    }
    
    // 对象池
    objectPools {
        contextPool {
            type "ExecutionContext"
            initialSize 100
            maxSize 1000
            
            factory { ->
                new ExecutionContext()
            }
            
            reset { context ->
                context.clear()
            }
        }
        
        bufferPool {
            type "ByteBuffer"
            initialSize 50
            maxSize 200
            
            factory { ->
                ByteBuffer.allocateDirect(8192)
            }
            
            reset { buffer ->
                buffer.clear()
            }
        }
    }
    
    // 内存映射
    memoryMapping {
        largeFiles {
            enabled true
            threshold "100MB"
            
            strategy "mmap"
            chunkSize "64MB"
        }
        
        cacheFiles {
            enabled true
            maxSize "1GB"
            
            eviction "lru"
        }
    }
    
    // 弱引用缓存
    weakReferences {
        nodeDefinitions {
            enabled true
            cleanupInterval 5.minutes
        }
        
        compiledScripts {
            enabled true
            cleanupInterval 10.minutes
        }
    }
}
```

## 11. 扩展机制

### 11.1 插件系统

```groovy
plugins {
    // 插件定义
    registry {
        scanPackages ["com.company.workflow.plugins"]
        
        autoload {
            enabled true
            patterns ["*Plugin.class", "*Extension.class"]
        }
        
        dependencies {
            resolver "maven"
            repositories ["central", "company-nexus"]
        }
    }
    
    // 节点扩展
    nodeExtensions {
        // 自定义节点类型
        register "customHttp" {
            class "com.company.plugins.CustomHttpNode"
            
            schema {
                properties {
                    url { type "string", required true }
                    method { type "string", default "GET" }
                    headers { type "object" }
                    body { type "any" }
                    timeout { type "duration", default "30s" }
                }
            }
            
            dsl {
                syntax """
                customHttp "调用API" {
                    url "https://api.example.com/data"
                    method "POST"
                    headers {
                        "Content-Type" "application/json"
                    }
                    body { context -> [id: context.id] }
                    timeout 60.seconds
                }
                """
            }
        }
        
        // 函数扩展
        register "math" {
            functions {
                add { a, b -> a + b }
                subtract { a, b -> a - b }
                multiply { a, b -> a * b }
                divide { a, b -> a / b }
                
                statistics {
                    mean { list -> list.sum() / list.size() }
                    median { list -> /* 中位数计算 */ }
                    stddev { list -> /* 标准差计算 */ }
                }
            }
            
            dsl {
                import "math.*"
                
                usage """
                calculate "数学运算" {
                    result { context ->
                        def values = context.numbers
                        def sum = add(values[0], values[1])
                        def avg = mean(values)
                        return [sum: sum, average: avg]
                    }
                }
                """
            }
        }
    }
    
    // 中间件扩展
    middlewareExtensions {
        // 执行前中间件
        beforeExecution {
            register "logging" {
                class "com.company.plugins.LoggingMiddleware"
                order 100
                
                config {
                    level "INFO"
                    includeContext true
                    sensitiveFields ["password", "token"]
                }
            }
            
            register "metrics" {
                class "com.company.plugins.MetricsMiddleware"
                order 200
                
                config {
                    namespace "workflow"
                    tags ["environment", "version"]
                }
            }
            
            register "security" {
                class "com.company.plugins.SecurityMiddleware"
                order 50
                
                config {
                    checkPermissions true
                    validateInput true
                    sanitizeOutput true
                }
            }
        }
        
        // 执行后中间件
        afterExecution {
            register "notification" {
                class "com.company.plugins.NotificationMiddleware"
                order 100
                
                config {
                    channels ["email", "slack"]
                    conditions {
                        onError true
                        onSuccess false
                        onTimeout true
                    }
                }
            }
        }
    }
    
    // 存储扩展
    storageExtensions {
        register "mongodb" {
            class "com.company.plugins.MongoDbStorage"
            
            config {
                uri "mongodb://localhost:27017"
                database "workflow"
                collection "executions"
            }
            
            capabilities ["persistence", "querying", "indexing"]
        }
        
        register "elasticsearch" {
            class "com.company.plugins.ElasticsearchStorage"
            
            config {
                cluster "elasticsearch-cluster"
                index "workflow-executions"
            }
            
            capabilities ["search", "analytics", "aggregation"]
        }
    }
}
```

### 11.2 API扩展

```groovy
apiExtensions {
    // REST API扩展
    restApi {
        // 自定义端点
        endpoints {
            "/api/workflows/{id}/analytics" {
                method "GET"
                handler "com.company.plugins.AnalyticsHandler"
                
                parameters {
                    path {
                        id { type "string", required true }
                    }
                    query {
                        timeRange { type "string", default "7d" }
                        metrics { type "array", items "string" }
                    }
                }
                
                responses {
                    200 {
                        schema {
                            type "object"
                            properties {
                                executions { type "integer" }
                                successRate { type "number" }
                                avgDuration { type "number" }
                                errors { type "array" }
                            }
                        }
                    }
                }
                
                security {
                    required true
                    roles ["analyst", "admin"]
                }
            }
        }
        
        // 中间件
        middleware {
            cors {
                enabled true
                allowedOrigins ["https://company.com"]
                allowedMethods ["GET", "POST", "PUT", "DELETE"]
                allowedHeaders ["Authorization", "Content-Type"]
            }
            
            rateLimit {
                enabled true
                requests 1000
                window "1h"
                keyResolver "ip"
            }
            
            compression {
                enabled true
                threshold 1024
                algorithms ["gzip", "deflate"]
            }
        }
    }
    
    // GraphQL API扩展
    graphqlApi {
        schema {
            types {
                WorkflowAnalytics {
                    fields {
                        executionCount "Int!"
                        successRate "Float!"
                        avgDuration "Float!"
                        errorDistribution "[ErrorCount!]!"
                    }
                }
                
                ErrorCount {
                    fields {
                        errorType "String!"
                        count "Int!"
                    }
                }
            }
            
            queries {
                workflowAnalytics {
                    args {
                        workflowId "String!"
                        timeRange "String"
                    }
                    type "WorkflowAnalytics"
                    resolver "com.company.resolvers.AnalyticsResolver"
                }
            }
            
            mutations {
                executeWorkflow {
                    args {
                        workflowId "String!"
                        input "JSON"
                    }
                    type "ExecutionResult"
                    resolver "com.company.resolvers.ExecutionResolver"
                }
            }
            
            subscriptions {
                executionUpdates {
                    args {
                        executionId "String!"
                    }
                    type "ExecutionStatus"
                    resolver "com.company.resolvers.SubscriptionResolver"
                }
            }
        }
        
        federation {
            enabled true
            serviceName "workflow-service"
            
            entities {
                Workflow {
                    keyFields ["id"]
                    resolveReference "com.company.resolvers.WorkflowReferenceResolver"
                }
            }
        }
    }
    
    // WebSocket API扩展
    websocketApi {
        endpoints {
            "/ws/executions/{executionId}" {
                handler "com.company.websocket.ExecutionWebSocketHandler"
                
                events {
                    onConnect { session, executionId ->
                        // 连接时的处理逻辑
                        subscribeToExecution(session, executionId)
                    }
                    
                    onMessage { session, message ->
                        // 消息处理逻辑
                        handleCommand(session, message)
                    }
                    
                    onClose { session, status ->
                        // 断开连接时的处理逻辑
                        unsubscribeFromExecution(session)
                    }
                }
                
                authentication {
                    required true
                    tokenLocation "query"  // query, header, cookie
                    tokenName "token"
                }
            }
        }
        
        broadcasting {
            channels {
                "execution.{executionId}" {
                    events ["started", "completed", "failed", "progress"]
                }
                
                "workflow.{workflowId}" {
                    events ["deployed", "deprecated"]
                }
            }
            
            persistence {
                enabled true
                ttl "24h"
            }
        }
    }
}
```

## 12. 总结

这个企业级 Groovy DSL 工作流系统提供了完整的解决方案，包括：

1. **丰富的DSL语法**: 支持各种业务场景的表达
2. **强大的执行引擎**: 异步、并行、容错处理
3. **完善的节点类型**: 逻辑、通信、数据处理等
4. **企业级特性**: 监控、安全、扩展性
5. **运维友好**: 容器化、K8s部署、监控告警
6. **高性能**: 缓存、优化、资源管理
7. **可扩展**: 插件系统、API扩展

该系统可以满足企业复杂的业务流程编排需求，同时具备良好的可维护性和扩展性。 